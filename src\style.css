@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background: #0a0a0a;
    background-image:
      radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    min-height: 100vh;
    color: #ffffff;
    line-height: 1.6;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .tech-card {
    @apply bg-gradient-to-br from-tech-light-gray to-tech-gray border border-tech-border rounded-xl backdrop-blur-sm;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .tech-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(0, 212, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  .tech-button {
    @apply px-6 py-3 bg-button-gradient text-white font-medium rounded-lg transition-all duration-300 relative overflow-hidden;
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.3);
  }

  .tech-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 24px rgba(0, 212, 255, 0.4);
  }

  .tech-button:active {
    transform: translateY(0);
  }

  .tech-input {
    @apply bg-tech-light-gray border border-tech-border rounded-lg px-4 py-3 text-white placeholder-gray-400 transition-all duration-300;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .tech-input:focus {
    @apply border-tech-blue ring-2 ring-tech-blue/20 outline-none;
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.3),
      0 0 0 3px rgba(0, 212, 255, 0.1);
  }

  .wallpaper-card {
    @apply relative overflow-hidden rounded-xl transition-all duration-300 cursor-pointer;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .wallpaper-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.6),
      0 0 0 1px rgba(0, 212, 255, 0.5);
  }

  .category-filter {
    @apply px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 cursor-pointer;
    background: rgba(42, 42, 42, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .category-filter:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.5);
  }

  .category-filter.active {
    background: linear-gradient(135deg, #00d4ff, #8b5cf6);
    border-color: transparent;
    color: white;
  }
}
