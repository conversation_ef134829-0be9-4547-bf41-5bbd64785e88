<template>
  <div class="wallpaper-card overflow-hidden group cursor-pointer" @click="$emit('view', wallpaper)">
    <!-- 图片容器 -->
    <div class="relative overflow-hidden">
      <img
        :src="wallpaper.thumbnail"
        :alt="wallpaper.title"
        class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
        @load="imageLoaded = true"
        @error="imageError = true"
      />
      
      <!-- 加载状态 -->
      <div v-if="!imageLoaded && !imageError" class="absolute inset-0 bg-gray-700 animate-pulse flex items-center justify-center">
        <div class="w-8 h-8 border-2 border-tech-blue border-t-transparent rounded-full animate-spin"></div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="imageError" class="absolute inset-0 bg-gray-700 flex items-center justify-center">
        <PhotoIcon class="w-12 h-12 text-gray-500" />
      </div>
      
      <!-- 悬停覆盖层 -->
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-3">
          <button 
            @click.stop="$emit('download', wallpaper)"
            class="p-3 bg-tech-blue rounded-full hover:bg-tech-blue/80 transition-colors transform hover:scale-110"
            title="下载"
          >
            <ArrowDownTrayIcon class="w-5 h-5 text-white" />
          </button>
          <button 
            @click.stop="$emit('favorite', wallpaper)"
            class="p-3 bg-tech-purple rounded-full hover:bg-tech-purple/80 transition-colors transform hover:scale-110"
            title="收藏"
          >
            <HeartIcon class="w-5 h-5 text-white" />
          </button>
        </div>
      </div>
      
      <!-- 分辨率标签 -->
      <div class="absolute top-2 left-2">
        <span class="px-2 py-1 bg-black bg-opacity-70 text-tech-blue text-xs font-medium rounded">
          {{ wallpaper.resolution }}
        </span>
      </div>
      
      <!-- 分类标签 -->
      <div class="absolute top-2 right-2">
        <span class="px-2 py-1 bg-tech-purple bg-opacity-80 text-white text-xs font-medium rounded">
          {{ wallpaper.categoryName }}
        </span>
      </div>

      <!-- 查看详情提示 -->
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-4 py-2 text-white text-sm font-medium">
            点击查看详情
          </div>
        </div>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="p-4">
      <h3 class="text-white font-medium text-sm mb-2 line-clamp-2 group-hover:text-tech-blue transition-colors">
        {{ wallpaper.title }}
      </h3>
      
      <div class="flex items-center justify-between text-xs text-gray-400">
        <div class="flex items-center space-x-3">
          <span class="flex items-center">
            <EyeIcon class="w-4 h-4 mr-1" />
            {{ formatNumber(wallpaper.views) }}
          </span>
          <span class="flex items-center">
            <ArrowDownTrayIcon class="w-4 h-4 mr-1" />
            {{ formatNumber(wallpaper.downloads) }}
          </span>
        </div>
        <span>{{ wallpaper.size }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  ArrowDownTrayIcon, 
  HeartIcon, 
  EyeIcon, 
  PhotoIcon 
} from '@heroicons/vue/24/outline'

// Props
defineProps({
  wallpaper: {
    type: Object,
    required: true
  }
})

// Emits
defineEmits(['download', 'favorite', 'view'])

// 响应式数据
const imageLoaded = ref(false)
const imageError = ref(false)

// 方法
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
