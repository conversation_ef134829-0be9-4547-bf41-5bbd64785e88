<template>
  <div v-if="wallpaper" class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- 背景遮罩 -->
    <div 
      class="absolute inset-0 bg-black bg-opacity-80 backdrop-blur-sm"
      @click="$emit('close')"
    ></div>
    
    <!-- 模态框内容 -->
    <div class="relative tech-card max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-slide-up">
      <!-- 关闭按钮 -->
      <button 
        @click="$emit('close')"
        class="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-all"
      >
        <XMarkIcon class="w-6 h-6" />
      </button>
      
      <!-- 图片区域 -->
      <div class="relative">
        <img 
          :src="wallpaper.fullImage"
          :alt="wallpaper.title"
          class="w-full h-64 md:h-80 object-cover rounded-t-xl"
          @load="imageLoaded = true"
        />
        
        <!-- 图片加载状态 -->
        <div v-if="!imageLoaded" class="absolute inset-0 bg-gray-700 animate-pulse rounded-t-xl flex items-center justify-center">
          <div class="w-12 h-12 border-4 border-tech-blue border-t-transparent rounded-full animate-spin"></div>
        </div>
        
        <!-- 分辨率标签 -->
        <div class="absolute top-4 left-4">
          <span class="px-3 py-1 bg-black bg-opacity-70 text-tech-blue text-sm font-medium rounded-full">
            {{ wallpaper.resolution }}
          </span>
        </div>
        
        <!-- 分类标签 -->
        <div class="absolute top-4 left-20">
          <span class="px-3 py-1 bg-tech-purple bg-opacity-80 text-white text-sm font-medium rounded-full">
            {{ wallpaper.categoryName }}
          </span>
        </div>
      </div>
      
      <!-- 详情内容 -->
      <div class="p-6">
        <!-- 标题和基本信息 -->
        <div class="mb-6">
          <h2 class="text-2xl font-bold text-white mb-2">{{ wallpaper.title }}</h2>
          <p class="text-gray-300 text-base leading-relaxed">{{ wallpaper.description }}</p>
        </div>
        
        <!-- 统计信息 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div class="text-center p-3 bg-tech-light-gray rounded-lg">
            <EyeIcon class="w-6 h-6 text-tech-blue mx-auto mb-1" />
            <div class="text-white font-medium">{{ formatNumber(wallpaper.views) }}</div>
            <div class="text-gray-400 text-sm">浏览量</div>
          </div>
          <div class="text-center p-3 bg-tech-light-gray rounded-lg">
            <ArrowDownTrayIcon class="w-6 h-6 text-tech-accent mx-auto mb-1" />
            <div class="text-white font-medium">{{ formatNumber(wallpaper.downloads) }}</div>
            <div class="text-gray-400 text-sm">下载量</div>
          </div>
          <div class="text-center p-3 bg-tech-light-gray rounded-lg">
            <DocumentIcon class="w-6 h-6 text-tech-orange mx-auto mb-1" />
            <div class="text-white font-medium">{{ wallpaper.size }}</div>
            <div class="text-gray-400 text-sm">文件大小</div>
          </div>
          <div class="text-center p-3 bg-tech-light-gray rounded-lg">
            <CalendarIcon class="w-6 h-6 text-tech-purple mx-auto mb-1" />
            <div class="text-white font-medium">{{ formatDate(wallpaper.uploadDate) }}</div>
            <div class="text-gray-400 text-sm">上传日期</div>
          </div>
        </div>
        
        <!-- 标签 -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-white mb-3">标签</h3>
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="tag in wallpaper.tags" 
              :key="tag"
              class="px-3 py-1 bg-tech-border text-gray-300 text-sm rounded-full"
            >
              #{{ tag }}
            </span>
          </div>
        </div>
        
        <!-- 作者信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-white mb-3">作者</h3>
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-r from-tech-blue to-tech-purple rounded-full flex items-center justify-center mr-3">
              <UserIcon class="w-6 h-6 text-white" />
            </div>
            <div>
              <div class="text-white font-medium">{{ wallpaper.author }}</div>
              <div class="text-gray-400 text-sm">专业设计师</div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex flex-col sm:flex-row gap-3">
          <button 
            @click="$emit('download', wallpaper)"
            class="flex-1 tech-button flex items-center justify-center"
          >
            <ArrowDownTrayIcon class="w-5 h-5 mr-2" />
            下载壁纸
          </button>
          <button 
            @click="$emit('favorite', wallpaper)"
            class="flex-1 px-6 py-3 bg-tech-light-gray border border-tech-border text-white font-medium rounded-lg transition-all duration-300 hover:bg-tech-border flex items-center justify-center"
          >
            <HeartIcon class="w-5 h-5 mr-2" />
            收藏
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  XMarkIcon, 
  EyeIcon, 
  ArrowDownTrayIcon, 
  HeartIcon,
  DocumentIcon,
  CalendarIcon,
  UserIcon
} from '@heroicons/vue/24/outline'

// Props
defineProps({
  wallpaper: {
    type: Object,
    default: null
  }
})

// Emits
defineEmits(['close', 'download', 'favorite'])

// 响应式数据
const imageLoaded = ref(false)

// 方法
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
