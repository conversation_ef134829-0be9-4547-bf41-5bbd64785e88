<template>
  <div class="min-h-screen">
    <!-- 导航栏 -->
    <NavBar
      :user="currentUser"
      @search="handleSearch"
      @login="showLoginModal = true"
    />

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 py-8">
      <!-- 分类筛选 -->
      <CategoryFilter
        :categories="categories"
        :selected-category="selectedCategory"
        @category-change="handleCategoryChange"
      />

      <!-- 搜索结果提示 -->
      <div v-if="searchQuery" class="mb-6 p-4 bg-tech-light-gray rounded-lg border border-tech-border">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <MagnifyingGlassIcon class="w-5 h-5 text-tech-blue mr-2" />
            <span class="text-white">搜索 "{{ searchQuery }}" 的结果：{{ filteredWallpapers.length }} 张壁纸</span>
          </div>
          <button
            @click="clearSearch"
            class="text-gray-400 hover:text-white transition-colors"
          >
            <XMarkIcon class="w-5 h-5" />
          </button>
        </div>
      </div>

      <!-- 壁纸网格 -->
      <WallpaperGrid
        :wallpapers="filteredWallpapers"
        :loading="loading"
        @download="handleDownload"
        @favorite="handleFavorite"
        @view="handleView"
      />
    </main>

    <!-- 登录模态框 -->
    <LoginModal
      v-if="showLoginModal"
      @close="showLoginModal = false"
      @login="handleLogin"
    />

    <!-- 壁纸详情模态框 -->
    <WallpaperDetailModal
      v-if="selectedWallpaper"
      :wallpaper="selectedWallpaper"
      @close="selectedWallpaper = null"
      @download="handleDownload"
      @favorite="handleFavorite"
    />

    <!-- 底部信息 -->
    <Footer />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/vue/24/outline'
import NavBar from './components/NavBar.vue'
import WallpaperGrid from './components/WallpaperGrid.vue'
import CategoryFilter from './components/CategoryFilter.vue'
import LoginModal from './components/LoginModal.vue'
import WallpaperDetailModal from './components/WallpaperDetailModal.vue'
import Footer from './components/Footer.vue'
import { mockWallpapers, categories } from './data/mockData.js'

// 响应式数据
const wallpapers = ref([])
const loading = ref(true)
const searchQuery = ref('')
const selectedCategory = ref('all')
const showLoginModal = ref(false)
const currentUser = ref(null)
const selectedWallpaper = ref(null)

// 计算属性 - 过滤后的壁纸
const filteredWallpapers = computed(() => {
  let filtered = wallpapers.value

  // 分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(wallpaper => wallpaper.category === selectedCategory.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(wallpaper =>
      wallpaper.title.toLowerCase().includes(query) ||
      wallpaper.categoryName.toLowerCase().includes(query) ||
      wallpaper.description.toLowerCase().includes(query) ||
      wallpaper.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  return filtered
})

// 方法
const handleSearch = (query) => {
  searchQuery.value = query
  // 搜索时重置分类筛选
  if (query && selectedCategory.value !== 'all') {
    selectedCategory.value = 'all'
  }
}

const clearSearch = () => {
  searchQuery.value = ''
}

const handleCategoryChange = (categoryId) => {
  selectedCategory.value = categoryId
  // 切换分类时清除搜索
  if (searchQuery.value) {
    searchQuery.value = ''
  }
}

const handleView = (wallpaper) => {
  selectedWallpaper.value = wallpaper
  // 增加浏览量（模拟）
  wallpaper.views += 1
}

const handleDownload = (wallpaper) => {
  console.log('下载壁纸:', wallpaper.title)
  // 增加下载量（模拟）
  wallpaper.downloads += 1

  // 模拟下载提示
  showNotification('开始下载: ' + wallpaper.title, 'success')
}

const handleFavorite = (wallpaper) => {
  console.log('收藏壁纸:', wallpaper.title)
  // 这里可以添加收藏逻辑
  showNotification('已收藏: ' + wallpaper.title, 'success')
}

const handleLogin = (userData) => {
  currentUser.value = userData
  showLoginModal.value = false
  console.log('用户登录:', userData)
  showNotification('登录成功，欢迎 ' + userData.name, 'success')
}

const showNotification = (message, type = 'info') => {
  // 简单的通知实现（可以后续用更好的通知库替换）
  console.log(`[${type.toUpperCase()}] ${message}`)
}

// 生命周期
onMounted(async () => {
  // 模拟加载数据
  setTimeout(() => {
    wallpapers.value = mockWallpapers
    loading.value = false
  }, 1000)
})
</script>
