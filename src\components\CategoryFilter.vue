<template>
  <div class="category-filter-container mb-8">
    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
      <FunnelIcon class="w-5 h-5 mr-2 text-tech-blue" />
      壁纸分类
    </h3>
    
    <div class="flex flex-wrap gap-3">
      <button
        v-for="category in categories"
        :key="category.id"
        :class="[
          'category-filter',
          { 'active': selectedCategory === category.id }
        ]"
        @click="$emit('categoryChange', category.id)"
      >
        <span>{{ category.name }}</span>
        <span class="ml-2 text-xs opacity-75">({{ category.count }})</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { FunnelIcon } from '@heroicons/vue/24/outline'

// Props
defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategory: {
    type: String,
    default: 'all'
  }
})

// Emits
defineEmits(['categoryChange'])
</script>

<style scoped>
.category-filter-container {
  animation: fadeIn 0.5s ease-in-out;
}
</style>
