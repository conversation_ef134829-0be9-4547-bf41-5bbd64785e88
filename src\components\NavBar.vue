<template>
  <nav class="tech-card mx-4 mt-4 p-4 sticky top-4 z-50">
    <div class="flex items-center justify-between">
      <!-- <PERSON><PERSON>和标题 -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-to-r from-tech-blue to-tech-purple rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">哲</span>
          </div>
          <h1 class="text-xl font-bold bg-gradient-to-r from-tech-blue to-tech-purple bg-clip-text text-transparent">
            哲风壁纸
          </h1>
        </div>
        
        <!-- 导航菜单 -->
        <div class="hidden md:flex items-center space-x-6 ml-8">
          <a 
            v-for="item in navItems" 
            :key="item.name"
            href="#"
            :class="[
              'px-3 py-2 rounded-lg transition-all duration-300 hover:bg-tech-blue/20',
              item.active ? 'bg-tech-blue/30 text-tech-blue' : 'text-gray-300 hover:text-white'
            ]"
            @click="setActive(item.name)"
          >
            {{ item.name }}
          </a>
        </div>
      </div>
      
      <!-- 搜索栏 -->
      <div class="flex-1 max-w-md mx-8">
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            v-model="searchInput"
            type="text"
            placeholder="搜索壁纸、分类、标签..."
            class="tech-input w-full pl-10 pr-10"
            @input="handleSearch"
            @keyup.enter="handleSearch"
            @focus="searchFocused = true"
            @blur="searchFocused = false"
          />
          <!-- 清除搜索按钮 -->
          <button
            v-if="searchInput"
            @click="clearSearch"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
          >
            <XMarkIcon class="w-5 h-5" />
          </button>

          <!-- 搜索建议（可选功能） -->
          <div v-if="searchFocused && searchSuggestions.length > 0" class="absolute top-full left-0 right-0 mt-2 bg-tech-light-gray border border-tech-border rounded-lg shadow-lg z-50">
            <div
              v-for="suggestion in searchSuggestions"
              :key="suggestion"
              @click="selectSuggestion(suggestion)"
              class="px-4 py-2 hover:bg-tech-border cursor-pointer text-gray-300 hover:text-white transition-colors"
            >
              {{ suggestion }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 用户区域 -->
      <div class="flex items-center space-x-4">
        <div v-if="user" class="flex items-center space-x-3">
          <img 
            :src="user.avatar" 
            :alt="user.name"
            class="w-8 h-8 rounded-full border-2 border-tech-blue"
          />
          <div class="hidden sm:block text-sm">
            <div class="text-white">{{ user.name }}</div>
            <div class="text-gray-400 text-xs">
              下载：{{ user.downloads }} | 收藏：{{ user.favorites }}
            </div>
          </div>
        </div>
        <div v-else>
          <button 
            @click="$emit('login')"
            class="tech-button text-sm"
          >
            登录
          </button>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <button 
          @click="showMobileMenu = !showMobileMenu"
          class="md:hidden p-2 rounded-lg hover:bg-gray-700 transition-colors"
        >
          <Bars3Icon class="w-6 h-6" />
        </button>
      </div>
    </div>
    
    <!-- 移动端菜单 -->
    <div v-if="showMobileMenu" class="md:hidden mt-4 pt-4 border-t border-gray-700">
      <div class="flex flex-col space-y-2">
        <a 
          v-for="item in navItems" 
          :key="item.name"
          href="#"
          :class="[
            'px-3 py-2 rounded-lg transition-all duration-300',
            item.active ? 'bg-tech-blue/30 text-tech-blue' : 'text-gray-300 hover:text-white hover:bg-tech-blue/20'
          ]"
          @click="setActive(item.name)"
        >
          {{ item.name }}
        </a>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed } from 'vue'
import { MagnifyingGlassIcon, Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'

// Props
defineProps({
  user: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['search', 'login'])

// 响应式数据
const searchInput = ref('')
const searchFocused = ref(false)
const showMobileMenu = ref(false)
const navItems = ref([
  { name: '壁纸社区', active: false },
  { name: '电脑壁纸', active: true },
  { name: '手机壁纸', active: false },
  { name: '头像制作', active: false }
])

// 搜索建议
const searchSuggestions = computed(() => {
  if (!searchInput.value || searchInput.value.length < 2) return []

  const suggestions = [
    '科技', '自然', '抽象', '星空', '城市', '建筑',
    '蓝色', '紫色', '绿色', '黑色',
    '4K', '8K', '高清',
    '现代', '简约', '梦幻', '炫酷'
  ]

  return suggestions
    .filter(suggestion =>
      suggestion.toLowerCase().includes(searchInput.value.toLowerCase())
    )
    .slice(0, 5)
})

// 方法
const handleSearch = () => {
  emit('search', searchInput.value)
  searchFocused.value = false
}

const clearSearch = () => {
  searchInput.value = ''
  emit('search', '')
}

const selectSuggestion = (suggestion) => {
  searchInput.value = suggestion
  handleSearch()
}

const setActive = (itemName) => {
  navItems.value.forEach(item => {
    item.active = item.name === itemName
  })
  showMobileMenu.value = false
}
</script>
