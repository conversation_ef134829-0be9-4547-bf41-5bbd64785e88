<template>
  <div class="wallpaper-grid">
    <!-- 加载状态 -->
    <div v-if="loading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <div 
        v-for="n in 12" 
        :key="n"
        class="tech-card p-4 animate-pulse"
      >
        <div class="bg-gray-700 rounded-lg h-48 mb-3"></div>
        <div class="bg-gray-700 rounded h-4 mb-2"></div>
        <div class="bg-gray-700 rounded h-3 w-2/3"></div>
      </div>
    </div>
    
    <!-- 壁纸网格 -->
    <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <WallpaperCard
        v-for="wallpaper in wallpapers"
        :key="wallpaper.id"
        :wallpaper="wallpaper"
        @download="$emit('download', wallpaper)"
        @favorite="$emit('favorite', wallpaper)"
        @view="$emit('view', wallpaper)"
      />
    </div>
    
    <!-- 空状态 -->
    <div v-if="!loading && wallpapers.length === 0" class="text-center py-16">
      <div class="text-gray-400 text-lg mb-4">没有找到相关壁纸</div>
      <div class="text-gray-500">尝试使用其他关键词搜索</div>
    </div>
  </div>
</template>

<script setup>
import WallpaperCard from './WallpaperCard.vue'

// Props
defineProps({
  wallpapers: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
defineEmits(['download', 'favorite', 'view'])
</script>

<style scoped>
.wallpaper-grid {
  min-height: 400px;
}
</style>
